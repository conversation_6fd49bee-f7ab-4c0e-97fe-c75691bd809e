import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { store } from '@/store';
import { setLogout } from '@/store/slices/authSlice';
import { LoginConfig, User } from '@/store/slices/authSlice';

// Knowledge Management API Types
export interface KnowledgeAreaResponse {
  id: string;
  name: string;
  description?: string;
  topicCount?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface KnowledgeTopicResponse {
  id: string;
  name: string;
  description?: string;
  unitCount?: number;
  areaId: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface KnowledgeUnitResponse {
  id: string;
  name: string;
  description?: string;
  stepCount?: number;
  topicId: string;
  createdAt?: string;
  updatedAt?: string;
  isDefault?: boolean;
  value?: Record<string, unknown>[]; // Content items for the unit
}

export interface CreateKnowledgeItemRequest {
  name: string;
  description?: string;
}

// User Management API Types
export interface UserResponse {
  id: string;
  name: string;
  employeeNo: string;
  email: string;
  phone?: string;
  dateOfJoining: string;
  division: string;
  department: string;
  location: string;
  pullRate?: number;
  knowledgeIndex?: number;
  status: 'active' | 'inactive' | 'blocked';
  groups?: string[];
  type: 'internal' | 'external';
}

export interface CreateUserRequest {
  name: string;
  employeeNo: string;
  email: string;
  phone?: string;
  dateOfJoining: string;
  division: string;
  department: string;
  location: string;
  type: 'internal' | 'external';
}

export interface UpdateUserRequest extends Partial<CreateUserRequest> {
  status?: 'active' | 'inactive' | 'blocked';
}

// File Upload API Types
export interface FileUploadResponse {
  files: {
    fieldname: string;
    originalname: string;
    encoding: string;
    mimetype: string;
    size: number;
    destination: string;
  }[];
  fields: Record<string, unknown>;
}



const API_BASE_URL = 'https://client-api.acuizen.com';

class ApiService {
  private axiosInstance: AxiosInstance;

  constructor(baseURL: string = API_BASE_URL) {
    this.axiosInstance = axios.create({
      baseURL,
      timeout: 30000, // 30 seconds timeout
      headers: {
        'Content-Type': 'application/json',
        'x-enterprise-id': 'koach',
      },
    });

    // Request interceptor to add auth token
    this.axiosInstance.interceptors.request.use(
      (config) => {
        const state = store.getState();
        const token = state.auth.tokens?.accessToken;

        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }

        // Log headers for debugging in development only
        if (process.env.NODE_ENV === 'development') {
          console.log('API Request Headers:', {
            'x-enterprise-id': config.headers['x-enterprise-id'],
            'Content-Type': config.headers['Content-Type'],
            'Authorization': config.headers.Authorization ? 'Bearer [TOKEN]' : 'Not set',
            url: config.url,
            method: config.method?.toUpperCase(),
          });
        }

        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle errors
    this.axiosInstance.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        if (error.response?.status === 401) {
          // Token expired or invalid, logout user
          store.dispatch(setLogout());
          window.location.href = '/login';
        }
        return Promise.reject(error);
      }
    );
  }

  async get<T>(endpoint: string): Promise<T> {
    try {
      const response = await this.axiosInstance.get<T>(endpoint);
      return response.data;
    } catch (error) {
      console.error('GET request failed:', error);
      throw error;
    }
  }

  async post<T>(endpoint: string, data?: unknown): Promise<T> {
    try {
      const response = await this.axiosInstance.post<T>(endpoint, data);
      return response.data;
    } catch (error) {
      console.error('POST request failed:', error);
      throw error;
    }
  }

  async put<T>(endpoint: string, data?: unknown): Promise<T> {
    try {
      const response = await this.axiosInstance.put<T>(endpoint, data);
      return response.data;
    } catch (error) {
      console.error('PUT request failed:', error);
      throw error;
    }
  }

  async delete<T>(endpoint: string): Promise<T> {
    try {
      const response = await this.axiosInstance.delete<T>(endpoint);
      return response.data;
    } catch (error) {
      console.error('DELETE request failed:', error);
      throw error;
    }
  }

  // File upload method
  async uploadFile<T>(endpoint: string, file: File, additionalData?: Record<string, unknown>): Promise<T> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      if (additionalData) {
        Object.keys(additionalData).forEach(key => {
          formData.append(key, String(additionalData[key]));
        });
      }

      const response = await this.axiosInstance.post<T>(endpoint, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      return response.data;
    } catch (error) {
      console.error('File upload failed:', error);
      throw error;
    }
  }

  /**
   * Fetches the login configuration from the API
   * @returns The login configuration
   */
  async getLoginConfig(): Promise<LoginConfig> {
    try {
      const response = await this.axiosInstance.get<LoginConfig>('/login-configs');
      console.log('Login config API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching login configuration:', error);
      throw error;
    }
  }

  /**
   * Fetches the current user's details from the API
   * @returns The user details
   */
  async getUserMe(): Promise<User> {
    try {
      const response = await this.axiosInstance.get<User>('/users/me');
      console.log('User details API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching user details:', error);
      throw error;
    }
  }

  /**
   * Fetches the current user's details from the API with explicit token
   * @param accessToken The access token to use for authentication
   * @returns The user details
   */
  async getUserMeWithToken(accessToken: string): Promise<User> {
    try {
      const headers = {
        'Authorization': `Bearer ${accessToken}`,
        'x-enterprise-id': 'koach'
      };

      console.log('Calling /users/me with headers:', {
        'Authorization': 'Bearer [TOKEN]',
        'x-enterprise-id': headers['x-enterprise-id']
      });

      const response = await this.axiosInstance.get<User>('/users/me', { headers });
      console.log('User details API response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching user details with token:', error);
      throw error;
    }
  }

  // Knowledge Management API Methods

  /**
   * Creates a new knowledge area
   * @param data The knowledge area data
   * @returns The created knowledge area
   */
  async createKnowledgeArea(data: CreateKnowledgeItemRequest): Promise<KnowledgeAreaResponse> {
    try {
      const response = await this.axiosInstance.post<KnowledgeAreaResponse>('/knowledge-areas', data);
      console.log('Knowledge area created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating knowledge area:', error);
      throw error;
    }
  }

  /**
   * Creates a new knowledge topic under a specific area
   * @param areaId The ID of the parent knowledge area
   * @param data The knowledge topic data
   * @returns The created knowledge topic
   */
  async createKnowledgeTopic(areaId: string, data: CreateKnowledgeItemRequest): Promise<KnowledgeTopicResponse> {
    try {
      const response = await this.axiosInstance.post<KnowledgeTopicResponse>(`/knowledge-areas/${areaId}/knowledge-topics`, data);
      console.log('Knowledge topic created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating knowledge topic:', error);
      throw error;
    }
  }

  /**
   * Creates a new knowledge unit under a specific topic
   * @param topicId The ID of the parent knowledge topic
   * @param data The knowledge unit data
   * @returns The created knowledge unit
   */
  async createKnowledgeUnit(topicId: string, data: CreateKnowledgeItemRequest): Promise<KnowledgeUnitResponse> {
    try {
      const response = await this.axiosInstance.post<KnowledgeUnitResponse>(`/knowledge-topics/${topicId}/knowledge-units`, data);
      console.log('Knowledge unit created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating knowledge unit:', error);
      throw error;
    }
  }

  /**
   * Fetches all knowledge areas
   * @returns Array of knowledge areas
   */
  async getKnowledgeAreas(): Promise<KnowledgeAreaResponse[]> {
    try {
      const response = await this.axiosInstance.get<KnowledgeAreaResponse[]>('/knowledge-areas');
      console.log('Knowledge areas fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching knowledge areas:', error);
      throw error;
    }
  }

  /**
   * Fetches knowledge topics for a specific area
   * @param areaId The ID of the knowledge area
   * @returns Array of knowledge topics
   */
  async getKnowledgeTopics(areaId: string): Promise<KnowledgeTopicResponse[]> {
    try {
      const response = await this.axiosInstance.get<KnowledgeTopicResponse[]>(`/knowledge-areas/${areaId}/knowledge-topics`);
      console.log('Knowledge topics fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching knowledge topics:', error);
      throw error;
    }
  }

  /**
   * Fetches knowledge units for a specific topic
   * @param topicId The ID of the knowledge topic
   * @returns Array of knowledge units
   */
  async getKnowledgeUnits(topicId: string): Promise<KnowledgeUnitResponse[]> {
    try {
      const response = await this.axiosInstance.get<KnowledgeUnitResponse[]>(`/knowledge-topics/${topicId}/knowledge-units`);
      console.log('Knowledge units fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching knowledge units:', error);
      throw error;
    }
  }

  /**
   * Fetches a specific knowledge unit by ID
   * @param unitId The ID of the knowledge unit
   * @returns The knowledge unit details
   */
  async getKnowledgeUnit(unitId: string): Promise<KnowledgeUnitResponse> {
    try {
      const response = await this.axiosInstance.get<KnowledgeUnitResponse>(`/knowledge-units/${unitId}`);
      console.log('Knowledge unit fetched successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching knowledge unit:', error);
      throw error;
    }
  }

  // User Management API Methods

  /**
   * Fetches users by type (internal or external) using bulk import endpoint
   * @param type The type of users to fetch ('internal' or 'external')
   * @returns Array of users
   */
  async getUsers(type: 'internal' | 'external'): Promise<UserResponse[]> {
    try {
      const response = await this.axiosInstance.post<UserResponse[]>('/users/bulk-import-from-file', {
        type
      });
      console.log(`${type} users fetched successfully:`, response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching ${type} users:`, error);
      throw error;
    }
  }

  /**
   * Creates a new user
   * @param userData The user data
   * @returns The created user
   */
  async createUser(userData: CreateUserRequest): Promise<UserResponse> {
    try {
      const response = await this.axiosInstance.post<UserResponse>('/users/external', userData);
      console.log('User created successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  }

  /**
   * Updates a user
   * @param userId The ID of the user to update
   * @param userData The updated user data
   * @param userType The type of user ('internal' or 'external')
   * @returns The updated user
   */
  async updateUser(userId: string, userData: UpdateUserRequest, userType: 'internal' | 'external'): Promise<UserResponse> {
    try {
      const response = await this.axiosInstance.put<UserResponse>(`/users/external/${userId}`, {
        ...userData,
        type: userType
      });
      console.log('User updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  }

  /**
   * Deletes a user
   * @param userId The ID of the user to delete
   * @param userType The type of user ('internal' or 'external')
   */
  async deleteUser(userId: string, userType: 'internal' | 'external'): Promise<void> {
    try {
      await this.axiosInstance.delete(`/users/external/${userId}`, {
        params: { type: userType }
      });
      console.log('User deleted successfully');
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  }

  /**
   * Blocks a user
   * @param userId The ID of the user to block
   * @param userType The type of user ('internal' or 'external')
   * @returns The updated user
   */
  async blockUser(userId: string, userType: 'internal' | 'external'): Promise<UserResponse> {
    try {
      return await this.updateUser(userId, { status: 'blocked' }, userType);
    } catch (error) {
      console.error('Error blocking user:', error);
      throw error;
    }
  }

  /**
   * Activates a user
   * @param userId The ID of the user to activate
   * @param userType The type of user ('internal' or 'external')
   * @returns The updated user
   */
  async activateUser(userId: string, userType: 'internal' | 'external'): Promise<UserResponse> {
    try {
      return await this.updateUser(userId, { status: 'active' }, userType);
    } catch (error) {
      console.error('Error activating user:', error);
      throw error;
    }
  }

  /**
   * Gets a presigned URL for file download
   * @param fileName The name of the file
   * @returns The presigned URL
   */
  async getFileDownloadUrl(fileName: string): Promise<string> {
    return this.get<string>(`/files/${fileName}/presigned-url`);
  }

  /**
   * Uploads a file to the /files endpoint
   * @param file The file to upload
   * @returns The upload response with file information
   */
  async uploadFileToFiles(file: File): Promise<FileUploadResponse> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await this.axiosInstance.post<FileUploadResponse>('/files', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      console.log('File uploaded successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw error;
    }
  }

  /**
   * Updates a knowledge unit with content items
   * @param unitId The ID of the knowledge unit
   * @param items The content items to save
   * @returns The updated knowledge unit
   */
  async updateKnowledgeUnitContent(unitId: string, items: Record<string, unknown>[]): Promise<KnowledgeUnitResponse> {
    try {
      const response = await this.axiosInstance.patch<KnowledgeUnitResponse>(`/knowledge-units/${unitId}`, { value: items });
      console.log('Unit content updated successfully:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error updating unit content:', error);
      throw error;
    }
  }
}

const apiService = new ApiService();
export default apiService;
